import React, { CSSProperties, FC, useEffect, useState } from 'react';
import classNames from 'classnames';
import { formatCompareValue } from './utils';
import { Flex, Modal } from 'antd';
import { CaretDownFilled, CaretUpFilled, QuestionCircleOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { DataDisplayItem } from '../handle-data';
import { useDataState } from '../store/useDataState';
import { traceClick, PageSPMKey, ModuleSPMKey } from '@/utils/trace';

export interface DataDisplayProps {
  items: DataDisplayItem[];
  lineCount?: number;
  className?: string;
  style?: CSSProperties;
  loading?: boolean;
}

const DataDisplayWrapper = styled.div`
  width: 100%;
  overflow: hidden;
  box-sizing: border-box;

  &.loading {
    .cell {
      background: #f5f5f5;
    }
  }
`;

const Row = styled.div`
  display: flex;
  width: 100%;

  & + & {
    .cell {
      border-top: none;
      margin-top: 18px;
    }
  }
`;

const Cell = styled.div`
  position: relative;
  flex-grow: 0;
  flex-shrink: 0;

  & + & {
    border-left: none;
  }

  p {
    padding: 0;
    margin: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
`;

const TipsIcon = styled(QuestionCircleOutlined)`
  margin-left: 4px;
  color: #ccc;
  flex-shrink: 0;
`;

const ValueWrap = styled.div`
  display: flex;
  flex-direction: row;
  align-items: baseline;
  margin: 5px 0;
`;

const Value = styled.div`
  overflow: hidden;
  font-size: 1.6em;
  color: #010000;
  text-overflow: ellipsis;
  font-weight: 600;
  flex-shrink: 0;
  font-family: 'AlibabaSans', 'PingFang SC', 'Helvetica Neue', Helvetica, STHeiTi, sans-serif;
`;

const Compare = styled.p`
  font-size: 1.2em;
  color: #aeadad;
  flex-shrink: 0;
`;

const CompareValue = styled.span<{ color: string }>`
  margin-left: 2px;
  color: ${(props) => props.color};
`;

const AdCount = styled.span`
  color: #666;
  background-color: #f4f6f9;
  font-size: 1em;
  border-radius: 0.4em;
  padding: 2px 0.8em 2px;
`;

const Bottom = styled.div`
  height: 0.8em;
  width: 100%;
`;

const Expand = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 1em;
  color: #010000;
  font-size: 1.4em;
`;

const ExpandIcon = styled.span`
  height: 1.5em;
  width: 1.5em;
  margin-left: 0.6em;
`;

const ExpandArrow = styled.div`
  margin-left: 3em;
  width: 0;
  height: 0;
  border: 0.8em solid #f4f6f9;
  border-left-color: transparent;
  border-right-color: transparent;
  border-top-color: transparent;
`;

const TargetLabel = styled.div`
  margin-right: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

export const DataDisplay: FC<DataDisplayProps> = ({
  items = [],
  lineCount = 3,
  className,
  style,
  loading,
}) => {
  const { isChild, pid } = useDataState();
  const [expanded, setExpanded] = useState(true);
  const [expandedTitle, setExpandedTitle] = useState('');
  const [expandRender, setExpandRender] = useState(null);

  useEffect(() => {
    if (!expandedTitle && expanded) {
      setExpandedTitle(items.find((item) => !!item.expandChildren)?.title);
    }
    if (expanded && expandedTitle) {
      setExpandRender(items.find((item) => item.title === expandedTitle)?.expandChildren || null);
    }
  }, [items, expandedTitle]);

  if (!items?.length) return null;

  const rows: Array<DataDisplayItem[] | null | unknown> = [];
  items.forEach((x) => {
    const lastItem = rows?.[rows.length - 1];
    if (
      !lastItem ||
      !Array.isArray(lastItem) ||
      (Array.isArray(lastItem) && lastItem.length === lineCount)
    ) {
      rows.push([x]);
    } else if (Array.isArray(lastItem)) {
      lastItem.push(x);
    }
    if (x.title === expandedTitle && expandRender) {
      rows.push(expandRender);
    }
  });

  const renderCompareItem = (value: number | string | undefined) => {
    const [compareColor, arrow, parsedCompareValue] = formatCompareValue(value);
    return (
      <Compare>
        <CompareValue color={compareColor}>
          {arrow}
          {parsedCompareValue}
        </CompareValue>
      </Compare>
    );
  };

  const onClickExpand = (title, _expandChildren) => {
    // 添加明细点击埋点
    traceClick(PageSPMKey.首页, ModuleSPMKey['喜报.明细'], {
      field: title,
      action: expanded ? '收起明细' : '展开明细',
      pid,
    });

    setExpanded(!expanded);
    setExpandRender(_expandChildren);
    setExpandedTitle(title);
    if (expanded) {
      setExpandRender(null);
    } else {
      setExpandRender(_expandChildren);
    }
  };

  return (
    <DataDisplayWrapper className={classNames(className, loading && 'loading')} style={style}>
      {rows.map((row, rowIndex) => (
        <Row key={rowIndex}>
          {Array.isArray(row)
            ? row.map((cell, cellIndex) => {
                const { title, value, tips, compareValue, adCount, expandText, expandChildren } =
                  cell || {};
                const isEmptyValue = typeof value === 'undefined' || value === null;
                return (
                  <Cell key={cellIndex} style={{ width: `${100 / lineCount}%` }}>
                    <Flex
                      align="center"
                      style={{ color: '#666665' }}
                      onClick={() => {
                        if (!tips) {
                          return;
                        }
                        Modal.info({
                          title,
                          content: tips,
                        });
                      }}
                    >
                      <TargetLabel style={{ fontSize: title.length > 7 ? '1em' : '1.2em' }}>
                        {title}
                      </TargetLabel>
                      {tips && isChild ? <TipsIcon id="empty" /> : null}
                    </Flex>
                    <ValueWrap>
                      <Value>{isEmptyValue ? '--' : value}</Value>
                      {!isEmptyValue &&
                        typeof compareValue !== 'undefined' &&
                        renderCompareItem(compareValue)}
                    </ValueWrap>
                    {(() => {
                      // 根据同行差值显示高于或低于同行
                      const peersDiffKey = `${cell.key}_vs_peers_diff`;
                      const peersDiff = cell[peersDiffKey];
                      if (peersDiff !== undefined && peersDiff !== null && peersDiff !== 0) {
                        const diffValue = parseFloat(peersDiff);
                        if (diffValue > 0) {
                          return (
                            <span style={{ display: 'flex', alignItems: 'center', gap: '2px' }}>
                              <img
                                src="https://img.alicdn.com/imgextra/i4/O1CN01PoHXns1DR93N1FYCC_!!6000000000212-55-tps-12-20.svg"
                                alt="高于"
                                style={{ width: '12px', height: '20px' }}
                              />
                              高于同行
                            </span>
                          );
                        } else if (diffValue < 0) {
                          return (
                            <span style={{ display: 'flex', alignItems: 'center', gap: '2px' }}>
                              <img
                                src="https://img.alicdn.com/imgextra/i2/O1CN01KkDr2D1cMtm8vVVst_!!6000000003587-55-tps-12-20.svg"
                                alt="低于"
                                style={{ width: '12px', height: '20px' }}
                              />
                              低于同行
                            </span>
                          );
                        }
                      }
                      return '同行';
                    })()}
                    <AdCount>广告+{adCount}</AdCount>
                    {expandText && expandChildren ? (
                      <>
                        <Expand onClick={() => onClickExpand(title, expandChildren)}>
                          {expandText}
                          <ExpandIcon as={expanded ? CaretDownFilled : CaretUpFilled} />
                        </Expand>
                        {expanded && <ExpandArrow />}
                      </>
                    ) : null}
                    {!expanded && <Bottom />}
                  </Cell>
                );
              })
            : row}
        </Row>
      ))}
    </DataDisplayWrapper>
  );
};
