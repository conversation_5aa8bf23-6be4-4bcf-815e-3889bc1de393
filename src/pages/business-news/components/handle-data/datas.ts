import { dataDisplayFactory } from '../utils';

const commonFieldMap = {
  智能体曝光量: {
    key: 'entry_exp_uv',
    desc: '在门店内智能体模块的所有曝光pv，包含欢迎态、固定位等所有不同位置的去重曝光',
  },
  智能体使用量: {
    key: 'entry_clk_uv',
    desc: '智能体访问量 （pv）',
  },
  智能体会话量: {
    key: 'msg_cnt',
    desc: '智能体总对话消息数，比如用户和智能体回复算作两条消息数',
  },
  平均有效对话轮次: {
    key: 'msg_cnt_rate',
    desc: '在智能体对话模块，用户与智能体之间进行的有效交互次数',
  },
  引导到店数: {
    key: 'arrive_20m_uv',
    desc: '用户点击智能体后，通过到店导航、打车到店、到店订单核销的用户行为次数之和',
  },
  留资卡片: {
    key: 'leads_card_exp_uv',
    desc: '在智能体对话内，发生的有效转化推荐次数，包括团购、订座、留资等',
  },
  服务卡片: {
    key: 'serve_card_exp_uv',
    desc: '在智能体对话内，发生的有效转化推荐次数，包括团购、订座、留资等',
  },
  智能体导购次数: {
    key: 'leads_serve_card_exp_uv',
    desc: '在智能体对话内，发生的有效转化推荐次数，包括团购、订座、留资等',
  },
  有效播报次数: {
    key: 'voice_msg_cnt',
    desc: '行中成功语音播报的次数，指完整播放',
  },
  商家相册数: {
    desc: '商家上传的相册数',
    key: 'shop_pic_cnt',
  },
};
export const foodFieldMap = {
  ...commonFieldMap,
  门店曝光量: {
    key: 'exposure_pv',
    desc: '在高德app上门店维度的曝光pv',
  },
  在线订座量: {
    key: 'booking_cnt',
    desc: '在线订座的数量',
  },
  门店访问量: {
    key: 'store_view_pv',
    desc: '用户使用高德app导航到店的人次',
  },
  预约到店量: {
    key: 'shop_order_cnt',
    desc: '用户在高德产生的预约到店量=电话预订量+在线预订量+团购订单',
  },
  电话拨打量: {
    key: 'phone_call_order_cnt',
    desc: '通过高德平台接通的总电话量',
  },
  商品下单量: {
    key: 'order_cnt',
    desc: '团购总订单=团单（券包+代金券）+买单+自提',
  },
  广告预约到店成本: {
    key: 'settle_cost_rate',
    desc: '广告总消耗/预约到店量',
  },
  电话接通率: {
    key: 'phone_order_rate',
    desc: '电话预订量/电话拨打量',
  },
  在线订座接单率: {
    key: 'booking_rate',
    desc: '在线订座接单/在线订座下单',
  },
  商品核销率: {
    key: 'used_order_rate',
    desc: '团单核销量/团单下单量',
  },
  广告总消耗: {
    key: 'settle_cost',
    desc: '总广告消耗（含现金、红包消耗），用户到店+客资线索',
  },
  广告有效到店量: {
    key: 'arrive_poi_cnt',
    desc: '用户到店广告投放后，通过到店导航、打车到店、到店订单核销的用户行为次数之和',
  },
  广告有效到店成本: {
    key: 'arrive_settle_cost_rate',
    desc: 'CPC到店消耗/有效到店量',
  },
  广告曝光量: {
    key: 'valid_exposure_cnt',
    desc: '广告组件的曝光量（单门店下多个广告组件累计曝光量）',
  },
  广告点击量: {
    key: 'valid_click_cnt',
    desc: '广告组件的访问量（单门店下多个广告组件累计点击量）',
  },
  广告点击率: {
    key: 'valid_click_rate',
    desc: '广告点击量/广告曝光量',
  },
  商家质量分: {
    key: 'business_score',
    desc: '高德商家门店质量分',
  },
  引导到店占有率: {
    key: 'arrive_20m_uv_rate',
    desc: '智能体引导到店数 / 有效到店量',
  },
  商家招牌菜数: {
    key: 'shop_dish_cnt',
    desc: '商家上传的推荐菜数',
  },
};

export const otherFieldMap = {
  ...commonFieldMap,
  门店曝光量: {
    key: 'exposure_pv',
    desc: '顾客在高德地图上看到门店的次数（包含顾客使用地图、搜索等功能时看到该店的次数）',
  },
  导航到店量: {
    key: 'route_pv',
    desc: '顾客在高德地图发起店铺导航的数量',
  },
  商品数: {
    key: 'spu_cnt',
    desc: '门店在线商品的总数量',
  },
  成交订单数: {
    key: 'order_cnt',
    desc: '门店的成交笔数，包括高德、口碑、支付宝端',
  },
  成交金额: {
    key: 'order_price',
    desc: '门店的成交总金额，包括高德、口碑、支付宝端',
  },
  商品核销金额: {
    key: 'used_order_price',
    desc: '门店的核销总金额，包括高德、口碑、支付宝端',
  },
  商品核销量: {
    key: 'used_order_cnt',
    desc: '门店的核销笔数，包括高德、口碑、支付宝端',
  },
  新增评论数: {
    key: 'review_cnt',
    desc: '在高德、口碑、支付宝展示的新增评论数量',
  },
  广告曝光量: {
    key: 'valid_exposure_cnt',
    desc: '投放广告增加的门店被看见次数，包括用户到店和客资线索投放产品',
  },
  广告日均曝光量: {
    key: 'valid_exposure_cnt_per_day',
    desc: '广告投放期间，平均每天门店被看见的次数（广告曝光量/投放天数）',
  },
  广告点击量: {
    key: 'valid_click_cnt',
    desc: '投放广告增加的门店被访问次数，包括用户到店和客资线索投放产品',
  },
  广告点击率: {
    key: 'valid_click_rate',
    desc: '广告点击量/广告曝光量',
  },
  千次曝光成本: {
    key: 'settle_cost_per_thousand',
    desc: '每千次曝光产生的花费金额，（（广告总消耗/广告曝光量）*1000）',
  },
  日均现金消耗: {
    key: 'settle_cash_cost_per_day',
    desc: '广告投放的日均现金消耗，（现金消耗/投放天数）',
  },
  广告总消耗: {
    key: 'settle_cost',
    desc: '指定时间范围内，所选门店广告消耗，包括用户到店和客资线索投放产品',
  },
  现金消耗: {
    key: 'settle_cash_cost',
    desc: '广告投放的现金消耗，包括用户到店和客资线索投放产品',
  },
  红包消耗: {
    key: 'settle_coupon_cost',
    desc: '门店和商品广告投放的红包消耗（含现金红包、折扣红包），包括用户到店、客资线索',
  },
  客资线索消耗: {
    key: 'leads_settle_cost',
    desc: 'OCPC客资线索投放产品的广告总消耗（含现金、红包消耗）',
  },
  客资线索消耗占比: {
    key: 'leads_settle_cost_rate',
    desc: 'OCPC客资线索投放产品的广告总消耗占比（OCPC客资消耗/广告总消耗）',
  },
  进店留资率: {
    key: 'leads_ocpc_sum_cnt_rate',
    desc: '用户进入推广门店或商品页后产生留资行为的比例（客资量/客资线索广告点击量）',
  },
  日均广告客资量: {
    key: 'bill_leads_cnt_per_day',
    desc: '平均每日的有效客资量（有效客资量/投放天数）',
  },
  广告客资成本: {
    key: 'leads_settle_cost_rate',
    desc: '有效单客资的的平均推广成本（OCPC客资消耗/有效客资量）',
  },
  广告客资量: {
    key: 'bill_leads_cnt',
    desc: '在历史 7 天内新客资量（去除重复客资量）',
  },
  电话客资: {
    key: 'bill_call_leads_cnt',
    desc: '通过广告投放，获取的电话咨询有效客资量，包括高德、口碑、支付宝',
  },
  电话客资占比: {
    key: 'bill_call_leads_cnt_rate',
    desc: '电话咨询客资量占有效客资量的比例（电话客资/有效客资量）',
  },
  平台客资: {
    key: 'bill_platform_leads_cnt',
    desc: '通过广告投放，获取的有效平台客资量，包括高德、口碑、支付宝',
  },
  平台客资占比: {
    key: 'bill_platform_leads_cnt_rate',
    desc: '平台客资量占有效客资量的比例，（平台客资/客资量）',
  },
  订单客资: {
    key: 'bill_order_leads_cnt',
    desc: '通过广告投放，带来的门店下单有效客资量，包括高德、口碑、支付宝',
  },
  订单客资占比: {
    key: 'bill_order_leads_cnt_rate',
    desc: '门店下单的客资量占有效客资量的比例（订单客资/有效客资量）',
  },
  在线咨询客资: {
    key: 'bill_online_consultation_leads_cnt',
    desc: '通过广告投放，在高德地图与您发起聊天的顾客数量，，包括高德、口碑、支付宝端',
  },
  在线咨询客资占比: {
    key: 'bill_online_consultation_leads_cnt_rate',
    desc: '在高德地图与您发起聊天的顾客数量占有效客资量的比例（在线咨询客资/有效客资量）',
  },
  预约礼客资: {
    key: 'bill_shop_reservation_leads_cnt',
    desc: '通过广告投放，领取到店礼的有效客资量，包括高德、口碑、支付宝端',
  },
  预约礼客资占比: {
    key: 'bill_shop_reservation_leads_cnt_rate',
    desc: '领取到店礼的客资量占总客资量的比例（预约礼客资/有效客资量）',
  },
  到店预约客资: {
    key: 'bill_arrival_reservation_leads_cnt',
    desc: '通过广告投放，到店预约留资的有效客资量，包括高德、口碑、支付宝端',
  },
  到店预约客资占比: {
    key: 'bill_arrival_reservation_leads_cnt_rate',
    desc: '通过广告投放，快速预约留资的客资量占有效客资量的比例（到店预约礼客资/有效客资量）',
  },
  高德打车客资: {
    key: 'bill_gaode_hail_taxis_leads_cnt',
    desc: '通过广告投放，获取的高德打车留资用户量，包括高德、口碑、支付宝端',
  },
  高德打车客资占比: {
    key: 'bill_gaode_hail_taxis_leads_cnt_rate',
    desc: '通过广告投放，获取的高德打车留资用户量占有效客资量的比例（高德打车客资/有效客资量）',
  },
  不可见客资: {
    key: 'invisible_leads_cnt',
    desc: '您在广告未投放期间错过的广告客资数量',
  },
  不可见客资占比: {
    key: 'invisible_leads_cnt_rate',
    desc: '您在广告未投放期间错过的广告客资数量占客资量的比例（不可见客资量/客资量）',
  },
  用户到店消耗: {
    key: 'arrive_settle_cost',
    desc: '通过广告投放，用户点击所产生的消耗金额（含现金、红包消耗）',
  },
  用户到店消耗占比: {
    key: 'arrive_settle_cost_percent',
    desc: '通过广告投放，用户点击所产生的消耗金额占广告总消耗比例（CPC 到店消耗/广告总消耗）',
  },
  广告有效到店量: {
    key: 'arrive_poi_cnt',
    desc: '广告投放后，有效到您门店的顾客数，具体包括通过到店导航、到店打车、到店订单核销的行为次数之和',
  },
  广告有效到店成本: {
    key: 'arrive_settle_cost_rate',
    desc: '用户到店消耗/有效到店量',
  },
  来电量: {
    key: 'call_end_cnt',
    desc: '通过推广内容进入详情页并产生电话拨打并接通的个数（仅统计CPC到店的）',
  },
  错失客资: {
    key: 'miss_cust_res_cnt',
    desc: '您在没有投放客资通产品期间错过的客资量',
  },
  商家质量分: {
    key: 'business_score',
    desc: '高德商家门店质量分',
  },
  智能体留资占比: {
    key: 'ai_leads_card_exp_uv_rate',
    desc: '智能体留资量 /（有效客资量-平台客资）',
  },
  电话接通量: {
    key: 'phone_order_cnt',
    desc: '',
  },
  电话接通率: {
    key: 'phone_order_rate',
    desc: '',
  },
};

type Key = keyof typeof foodFieldMap | keyof typeof otherFieldMap;
interface KeyStruct {
  key: Key;
  children?: Array<Key | KeyStruct>;
  defaultHidden?: boolean;
  hiddenable?: boolean;
}

export const getFields = (
  fields: Array<Key | KeyStruct>,
  dataResult: Record<string, any>,
  isFood = false,
) => {
  const map = isFood ? foodFieldMap : otherFieldMap;
  return fields.filter(Boolean).map((field) => {
    const fieldKey = (field as KeyStruct)?.key || (field as Key);
    const finded = map[fieldKey];
    if (finded) {
      return {
        ...dataDisplayFactory(
          finded.key,
          fieldKey,
          dataResult[finded.key],
          dataResult[`${finded.key}_last_cycle`],
          finded.desc,
          (field as KeyStruct)?.children
            ? getFields((field as KeyStruct).children, dataResult, isFood)
            : undefined,
          dataResult,
        ),
        defaultHidden: (field as KeyStruct)?.defaultHidden,
        hiddenable: (field as KeyStruct)?.hiddenable,
      };
    }
    return null;
  });
};

export const getField = (field: Key, dataResult: Record<string, any>, isFood = false) => {
  const map = isFood ? foodFieldMap : otherFieldMap;
  const finded = map[field];
  const fieldKey = field;
  const displayData = dataDisplayFactory(
    finded.key,
    fieldKey,
    dataResult[finded.key],
    dataResult[`${finded.key}_last_cycle`],
    finded.desc,
    undefined,
    dataResult,
  );
  if (finded && displayData) {
    return displayData;
  }
  return {
    value: '-',
    compareValue: '-',
  };
};

export const getFieldKey = (field: Key, isFood = false) => {
  const map = isFood ? foodFieldMap : otherFieldMap;
  const finded = map[field];
  if (finded) {
    return finded.key;
  }
  return '';
};
export const getFieldKeys = (fields: Key[], isFood = false) => {
  const map = isFood ? foodFieldMap : otherFieldMap;
  return fields.map((fieldKey) => {
    const finded = map[fieldKey];
    if (finded) {
      return finded.key;
    }
    return '';
  });
};
export const getFieldsMap = (
  fields: Key[],
  isFood = false,
): Array<{
  key: string;
  title: string;
  desc: string;
}> => {
  const map = isFood ? foodFieldMap : otherFieldMap;
  return fields
    .map((item) => {
      if (map[item]) {
        return {
          ...map[item],
          title: item,
        };
      }
      return null;
    })
    .filter((item) => !!item);
};
