import React, { useEffect } from 'react';
import classNames from 'classnames';
import './index.less';
import { sendEvent } from '@/common/utils';
import styled from 'styled-components';
import { NewBusinessNewsType, PEER_SELECTOR_DATA_TYPES } from '../const';
import { useDataState } from '../store/useDataState';
import { traceClick, PageSPMKey, ModuleSPMKey } from '@/utils/trace';
import RedSelect from '../common/red-select';
import { queryPeersData } from '@/services';

interface IProps {
  fields: Array<{ key: string; title: string; children: any[]; [key: string]: any }>;
  hiddenFields: string[];
  onHiddenFieldsChange: (value, businessNewsType?: NewBusinessNewsType) => void;
  hiddenHistory: NewBusinessNewsType[];
  businessNewsType: NewBusinessNewsType;
  pid: string;
}

const Wrap = styled.div`
  background-color: rgba(0, 0, 0, 0.04);
  border-radius: 4px;
  padding: 10px;
  color: rgba(0, 0, 0, 0.85);
  margin-top: 8px;
  /* max-height: 300px;
  overflow-y: auto; */
`;

function handleShopFields(
  list: any[],
): Array<{ title: string; key: string; defaultHidden: boolean }> {
  const newFields = [];
  list.forEach((item) => {
    if (item.children || item.expandChildrenData) {
      newFields.push(...handleShopFields(item.children || item.expandChildrenData || []));
    } else {
      newFields.push(item);
    }
  });
  return newFields;
}

export default function WeeklyReportHiddenFields(props: IProps) {
  const { fields, onHiddenFieldsChange, hiddenFields, businessNewsType, hiddenHistory, pid } =
    props;
  const { fields: extFields, hiddenFields: extHiddenFields, onHiddenFieldChange } = useDataState();
  const handleClick = (item) => {
    if (item.render) {
      return;
    }
    const { key: value, title } = item;
    const newList = [...hiddenFields];
    let checkable;
    if (hiddenFields.includes(value)) {
      newList.splice(newList.indexOf(value), 1);
      checkable = false;
    } else {
      newList.push(value);
      checkable = true;
    }
    onHiddenFieldsChange(newList);
    sendEvent('HIDDEN_FIELDS', 'CLK', {
      c1: title,
      c2: checkable ? '隐藏字段' : '显示字段',
    });
    traceClick(PageSPMKey.首页, ModuleSPMKey['喜报.字段切换'], {
      field: title,
      action: checkable ? '隐藏字段' : '显示字段',
      pid,
    });
  };
  useEffect(() => {
    const flattenFields = handleShopFields(fields);
    if (!hiddenHistory.includes(businessNewsType)) {
      onHiddenFieldsChange(
        flattenFields.filter((item) => item.defaultHidden).map((item) => item.key),
        businessNewsType,
      );
    }
  }, [fields, businessNewsType]);

  const firstExtFields = extFields?.[0];
  const restExtFields = extFields?.slice(1);

  return (
    <div className="weekly-report-hidden-field">
      <div className="weekly-report-hidden-field-title">
        <div className="title">喜报字段隐藏</div>
        <span>
          下载时如需隐藏部分字段，点击后字段置灰不会在下载的图片中显示，再次点击可恢复显示（调整仅对下载的图片生效）
        </span>
      </div>
      <Wrap>
        {firstExtFields && (
          <HiddenFieldsWrap
            hiddenFields={extHiddenFields}
            fields={[firstExtFields]}
            handleClick={(item) => {
              onHiddenFieldChange(item.key);
            }}
          />
        )}
        <HiddenFieldsWrap {...props} handleClick={handleClick} allFields={fields} />
        {restExtFields.length ? (
          <HiddenFieldsWrap
            hiddenFields={extHiddenFields}
            fields={restExtFields}
            handleClick={(item) => {
              onHiddenFieldChange(item.key);
            }}
          />
        ) : null}
      </Wrap>
    </div>
  );
}

const Card = styled.div`
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  background-color: rgba(0, 0, 0, 0.04);
  border-radius: 4px;
  border: 1px solid #f5222d;
  color: rgba(0, 0, 0, 0.85);
  display: inline-block;
  margin-right: 8px;
  margin-bottom: 8px;
  font-size: 12px;
  &.item-checked {
    border: 1px solid #000;
    background-color: rgba(0, 0, 0, 0.25);
  }
`;

export function HiddenFieldsWrap(props: {
  fields: any[];
  handleClick: (item) => void;
  hiddenFields: string[];
  allFields?: any[];
}) {
  const { fields, handleClick, hiddenFields, allFields } = props;
  const { setPeerRange, getPeerRange, dateRange, shopIdList, isShow } = useDataState();

  return (
    <>
      {fields.map((row, index) => {
        if (row.children) {
          const needsSelector = PEER_SELECTOR_DATA_TYPES.includes(row.label as any);

          return (
            <div key={index}>
              <div
                style={{
                  fontSize: 15,
                  fontWeight: 500,
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                }}
              >
                {row.label}
                {
                  <>
                    <h4>展示同行均值对比</h4>
                    <RedSelect
                      defaultValue="NORMAL"
                      style={{ width: 140 }}
                      value={getPeerRange(row.label)}
                      options={[
                        { label: '周边5km范围', value: 'NORMAL' },
                        { label: '同区', value: 'DISTRICT' },
                        { label: '同城', value: 'CITY' },
                      ]}
                      onChange={async (value) => {
                        console.log(`同行范围切换: ${row.label} -> ${value}`);
                        setPeerRange(row.label, value);

                        if (dateRange?.[0] && dateRange?.[1] && shopIdList?.[0]) {
                          try {
                            // 获取当前分组下所有字段的key（包括嵌套字段）
                            const getAllFieldKeys = (fields: any[]): string[] => {
                              const keys: string[] = [];
                              fields.forEach((field) => {
                                if (field.key) {
                                  keys.push(field.key);
                                }
                                if (field.expandChildrenData) {
                                  keys.push(...getAllFieldKeys(field.expandChildrenData));
                                }
                              });
                              return keys;
                            };

                            // 1. 获取当前分组的所有字段
                            const currentGroupKeys = getAllFieldKeys(row.children || []);

                            // 2. 获取用户选中的字段（非隐藏的字段）
                            const getSelectedKeys = (allFields: any[]): string[] => {
                              const keys: string[] = [];
                              allFields.forEach((field) => {
                                if (field.children) {
                                  keys.push(...getSelectedKeys(field.children));
                                } else if (field.key && !hiddenFields.includes(field.key)) {
                                  keys.push(field.key);
                                }
                                if (field.expandChildrenData) {
                                  keys.push(...getSelectedKeys(field.expandChildrenData));
                                }
                              });
                              return keys;
                            };

                            const selectedKeys = getSelectedKeys(allFields || []);

                            // 3. 合并当前分组字段和用户选中的字段，去重
                            const allKeys = [...new Set([...currentGroupKeys, ...selectedKeys])];
                            const peerKeyList = allKeys.join(',');

                            const result = await queryPeersData({
                              shopId: shopIdList[0],
                              startDate: dateRange[0],
                              endDate: dateRange[1],
                              range: value,
                              peerKeyList,
                            });
                            console.log(`接口调用成功:`, result);
                          } catch (error) {
                            console.error(`接口调用失败:`, error);
                          }
                        }
                      }}
                    />
                  </>
                }
              </div>
              <HiddenFieldsWrap {...props} fields={row.children || row.expandChildrenData} />
            </div>
          );
        }
        const Render = row.render;
        if (Render) {
          return <Render key={index} />;
        }
        return (
          <>
            <Card
              className={classNames({
                'item-checked': hiddenFields.includes(row.key),
                item: true,
              })}
              onClick={() => handleClick(row)}
              key={row.key}
            >
              {row.title}
            </Card>
            {row.expandChildrenData?.length && (
              <HiddenFieldsWrap {...props} fields={row.expandChildrenData} />
            )}
          </>
        );
      })}
    </>
  );
}
