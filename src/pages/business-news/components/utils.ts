/* eslint-disable max-lines */
import numeral from 'numeral';

export const MONEY_UNIT = '(元)';

const formatNum = (
  value: string | number | undefined,
  format: string,
  handleValue?: (v: number) => number,
) => {
  return typeof value !== 'undefined'
    ? numeral(handleValue ? handleValue(+value) : value).format(format)
    : '--';
};

/**
 * 整数
 * @param num
 * @returns
 */
const formatCount = (num: string | number | undefined) => {
  return formatNum(num, '0,0');
};

/**
 * 保留一位小数
 * @param num
 * @returns
 */
const formatDecimals = (num: string | number | undefined) => {
  return formatNum(num, '0.0');
};

const formatMoney = (num: string | number | undefined) => {
  return formatNum(num, '0,0.0');
};

const formatRatio = (num: string | number | undefined) => {
  return Number(num) === 0 ? 0 : formatNum(num, '0,0.00%');
};

export const formatterTargetValue = (
  value: string | number,
  isMoney?: boolean,
  isRatio?: boolean,
) => {
  if (typeof value === 'undefined' || value === null) {
    return value;
  }
  const num = +value;
  if (num > 100000000) {
    return `${formatDecimals(num / 100000000)}亿`;
  } else if (num > 10000) {
    return `${formatDecimals(num / 10000)}万`;
  } else if (isMoney) {
    return formatMoney(value);
  } else if (isRatio) {
    return formatRatio(value);
  } else {
    return formatCount(value);
  }
};

export const dataDisplayFactory = (
  key,
  title,
  value,
  compareValue,
  tips,
  expandChildrenData?,
  dataResult?,
) => {
  const existExpandChildrenData = expandChildrenData
    ? {
        expandText: '明细',
        expandChildrenData,
      }
    : {};

  // 提取同行差值数据
  const peersDiffKey = `${key}_vs_peers_diff`;
  const peersKey = `${key}_vs_peers`;
  const peersDiff = dataResult?.[peersDiffKey];
  const peersValue = dataResult?.[peersKey];

  return {
    title,
    tips,
    value: formatterTargetValue(
      value,
      title.endsWith(MONEY_UNIT),
      title.endsWith('率') || title.endsWith('占比'),
    ),
    compareValue,
    key,
    // 添加同行相关数据
    ...(peersDiff !== undefined && { [peersDiffKey]: peersDiff }),
    ...(peersValue !== undefined && { [peersKey]: peersValue }),
    ...existExpandChildrenData,
  };
};
export const inPuppeteerCtx = () => {
  return navigator.userAgent.includes('PUPPETEER_CLIP_IMAGE');
};
