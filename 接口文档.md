# 接口文档

---

数仓字段：[请至钉钉文档查看附件《本期新增字段映射（喜报）》](https://alidocs.dingtalk.com/i/nodes/G53mjyd80KXOR2zkfQMBr5pZJ6zbX04v?doc_type=wiki_doc&iframeQuery=anchorId%3DX02mejre8ucg0c43vfgh2&rnd=0.22085570304267488)

本次会新增针与同行的比较，于同行的比较值差值统一以 \_vs\_peers\_diff结尾。举例：exposure\_pv（曝光量PV），exposure\_pv\_vs\_peers\_diff（曝光量PV与同行差值）

同行均值统一以 \_vs\_peers结尾举例：exposure\_pv（曝光量PV），exposure\_pv\_vs\_peers（曝光量PV同行均值）

---

# 灰度接口

#### 接口定义

```sql
amap-sales-data.PageConfigQueryGwFacade.queryPageConfig
```

#### 请求参数

~~com.amap.sales.data.client.dto.domainmodel.request.ConfigQueryRequest~~

| ~~**参数名**~~ | ~~**类型**~~ | ~~**必填**~~ | ~~**说明**~~ | ~~**示例值**~~ |
| --- | --- | --- | --- | --- |
| ~~sceneCode~~ | ~~String~~ | ~~是~~ | ~~场景名~~ | ~~传固定值：~~~~**XIBAO\_CHANGE\_DATA\_SOURCE**~~ |
| ~~requestChannel~~ | ~~String~~ | ~~是~~ | ~~来源~~ | ~~XUANYUAN("轩辕")~~ |
| ~~requestParams~~ | ~~Map~~ | ~~否~~ | ~~额外传参~~ | ~~本期不需要~~ |

#### 响应结构

| ~~**参数名**~~ | ~~**类型**~~ | ~~**说明**~~ | ~~**示例值**~~ |
| --- | --- | --- | --- |
| ~~success~~ | ~~Boolean~~ | ~~是否执行成功~~ | ~~true~~ |
| ~~code~~ | ~~String~~ | ~~响应码~~ | ~~"0000"~~ |
| ~~msgInfo~~ | ~~String~~ | ~~响应信息~~ | ~~"调用成功"~~ |
| ~~data~~ | ~~Object~~ | ~~业务响应数据~~ | ~~见示例~~ |

#### 响应示例

```sql
{
    "result": true,
    "code": "00000",
    "message": null,
    "version": "1.0",
    "timestamp": "1750301992444",
    "success": true,
    "msgInfo": "调用成功",
    "msgCode": "SUCCESS",
    "traceId": "213eface17503019922917061e0d3d",
    "data": {
        "data": {
            "totalSize": null,
            "sceneCode": null,
            "pageNo": null,
            "success": false,
            "values": null,
            "pageSize": null,
            "errorCode": null,
            "keyDesc": null,
            "retry": false,
            "extInfo": {},
            "errorMsg": null,
            "specialValues": [
                {
                    "keyValue": "true",
                    "keyDesc": "喜报数据源切换命中灰度",
                    "key": "XIBAO_GREY_HIT"
                }
            ]
        },
        "success": true,
        "resultCode": "SUCCESS",
        "resultMessage": "成功",
        "extInfo": {
            "traceId": "212d56e017506829085372191e157f"
        }
    },
    "remoteRequestInfo": []
}

```

# 发起诊断

#### 接口定义

```json
amap-sales-data.SmartDiagnoseGwFacade.startDiagnose
```

#### 请求参数

| **参数名** | **类型** | **是否必填** | **说明** | **示例值** |
| --- | --- | --- | --- | --- |
| sceneCode | String | 是 | 诊断场景，固定值 | "**XIBAO\_SMART\_DIAGNOSE**" |
| requestChannel | String | 是 | 来源 | XUANYUAN("轩辕") |
| params | Map | 是 | 请求参数 | \- |
| params.shopIdList | String | 与pid二选一 | 门店ID列表 | "xgc\_20241218115556755grku"，多个英文逗号“ ,” 隔开 |
| params.pid | String | 与shopIdList二选一 | pid | "2088197979898989"，传了门店ID，则不生效pid筛选 |
| params.startDate | String | 是 | 开始日期(yyyyMMdd) | "20250601" |
| params.endDate | String | 是 | 结束日期(yyyyMMdd) | "20250607" |

#### 响应结构

| **参数名** | **类型** | **说明** | **示例值** |
| --- | --- | --- | --- |
| success | Boolean | 是否执行成功 | true |
| code | String | 响应码 | "0000" |
| msgInfo | String | 响应信息 | "调用成功" |
| data | BizResult | 业务响应数据 | 见示例 |

**BizResult**

| **参数名** | **类型** | **说明** | **示例值** |
| --- | --- | --- | --- |
| success | Boolean | 是否执行成功 | true |
| resultCode | String | 错误码 | "SUCCESS" |
| resultMessage | String | 错误原因 | "成功" |
| data | Boolean | 是否发起诊断成功 | true |
| extInfo | Map<String, String> | 补充信息 | 见示例 |

响应示例

```plaintext
{
    "result": true,
    "code": "00000",
    "message": null,
    "version": "1.0",
    "timestamp": "1750301992444",
    "success": true,
    "msgInfo": "调用成功",
    "msgCode": "SUCCESS",
    "traceId": "213eface17503019922917061e0d3d",
    "data": {
        "taskId":"11223344553"
    },
    "remoteRequestInfo": []
}
```

# 轮询诊断结果

#### 接口定义

```json
amap-sales-data.SmartDiagnoseGwFacade.queryDiagnoseResult
```

#### 请求参数

| **参数名** | **类型** | **是否必填** | **说明** | **示例值** |
| --- | --- | --- | --- | --- |
| taskId | String | 是 | 任务id | 任务ID |

#### 响应结构

| **参数名** | **类型** | **说明** | **示例值** |
| --- | --- | --- | --- |
| success | Boolean | 是否执行成功 | true |
| code | String | 响应码 | "0000" |
| msgInfo | String | 响应信息 | "调用成功" |
| data | BizResult | 业务响应数据 | 见示例 |

**BizResult**

| **参数名** | **类型** | **说明** | **示例值** |
| --- | --- | --- | --- |
| success | Boolean | 是否执行成功 | true |
| resultCode | String | 错误码 | "SUCCESS" |
| resultMessage | String | 错误原因 | "成功" |
| data | String | 大模型输出结果 | 见示例 |
| extInfo | Map<String, String> | 补充信息 | 见示例 |

**响应示例**

```plaintext
{
    "result": true,
    "code": "00000",
    "message": null,
    "version": "1.0",
    "timestamp": "1750301992444",
    "success": true,
    "msgInfo": "调用成功",
    "msgCode": "SUCCESS",
    "traceId": "213eface17503019922917061e0d3d",
    "data": {
      "taskId": "112233444,
      "status": "DONE",
      "result": "老板您好，门店数据方面，小德为您做以下分析
•流量数据：门店曝光访问数据近7天有小幅度提升，较同行在较离水平，但意向用于
较低，需要关注门店装修质量
•广告数据：广告总消耗274.65，环比下降7.04%。广告投入减少可能影响曝光和点
击量，需关注投放策略。
•互动数据：门店评论收藏等数据处于行业较低水平，需加强店内引导，发布到店礼
有助于提升哦-"
    },
    "remoteRequestInfo": []
}
```

# 查询门店汇总数据

---

年度喜报复用

#### 接口定义

```json
amap-sales-data.DataQueryGwFacade.queryData
```

#### 请求参数

| **参数名** | **类型** | **必填** | **说明** | **示例值** |
| --- | --- | --- | --- | --- |
| request | **DataQueryRequest** | 是 | 查询请求 | 见示例 |

**DataQueryRequest结构**

| **参数名** | **类型** | **是否必填** | **说明** | **示例值** |
| --- | --- | --- | --- | --- |
| applicationCode | String | 是 | 应用编码 | **"xibao\_shop\_overview\_0819****"** |
| requestChannel | String | 是 | 来源 | XUANYUAN("轩辕") |
| resultType | String | 是 | 返回类型 | 固定值：LIST |
| requestParams | Object | 是 | 请求参数 | \- |
| requestParams.shopIdList | String | 与pid二选一 | 门店ID列表 | "xgc\_20241218115556755grku"，多个英文逗号“ ,” 隔开 |
| requestParams.pid | String | 与shopIdList二选一 | pid | "2088197979898989"，传了门店ID，则不生效pid筛选 |
| requestParams.startDate | String | 是 | 开始日期(yyyyMMdd) | "20250601" |
| requestParams.endDate | String | 是 | 结束日期(yyyyMMdd) | "20250607" |

#### 响应结构

| **参数名** | **类型** | **说明** | **示例值** |
| --- | --- | --- | --- |
| success | Boolean | 是否执行成功 | true |
| code | String | 响应码 | "0000" |
| msgInfo | String | 响应信息 | "调用成功" |
| data | BizResult | 业务响应数据 | 见示例 |

**BizResult**

| **参数名** | **类型** | **说明** | **示例值** |
| --- | --- | --- | --- |
| success | Boolean | 是否执行成功 | true |
| resultCode | String | 错误码 | "SUCCESS" |
| resultMessage | String | 错误原因 | "成功" |
| data | QueryResponse | 查询结果 | 见示例 |
| extInfo | Map<String, String> | 补充信息 | 见示例 |

**QueryResponse结构**

| **参数名** | **类型** | **说明** | **示例值** |
| --- | --- | --- | --- |
| applicationDataList | List<ApplicationDataVO> | 每个应用applicationCode的数据 | 见示例 |

**ApplicationDataVO结构**

| **参数名** | **类型** | **说明** | **示例值** |
| --- | --- | --- | --- |
| applicationCode | String | 应用code | 固定值 |
| ruleDataList | List<RuleDataVO> | 返回数据 | 见示例 |

#### 响应示例

```json
{
  "success": true,
  "errorCode": "SUCCESS",
  "class": "com.amap.sales.data.client.dto.domainmodel.result.Result",
  "value": {
    "applicationDataList": [
      {
        "ruleDataList": [
          {
            "ruleCode": "xibao_overview_data_rule",
            "indicatorList": [],
            "success": true,
            "values": [
              {
                "score_level":"LV1，门店V标，单店生效",
                "business_situation":"经营现状（良好、中等、较差）",
                "sign_duration":"入驻时长（连续X年）"
                "ad_clk_pv_rate": "0.0263",
                "pic_cnt_plus_last_cycle": "-0.061",
                "ord_cnt": "12",
                "ai_arrive_20m_uv_rate_last_cycle": "0.131",
                "arrive_kz_cnt": "0",
                "ai_msg_cnt": "1820",
                "invisible_kz_cnt": "4",
                "item_cnt": "52",
                "ai_arrive_20m_uv_rate": "0.0405",
                "orig_call_end_cnt": "33",
                "ad_cust_res_cost": "63900.00",
                "ai_exp_uv": "132",
                "ai_voice_msg_cnt": "3233",
                "ai_arrive_20m_uv_last_cycle": "3.583",
                "use_suborder_cn": "9",
                "item_cnt_last_cycle": "0.106",
                "ad_exp_pv_per_day_last_cycle": "3.703",
                "gd_exp_pv": "1121907",
                "ad_cust_res_cost_per_thousand": "66.35",
                "consult_kz_cnt_last_cycle": "5.714",
                "settle_cash_cost": "46701.00",
                "wp_offline_days": "0",
                "clk_call_pv": "180",
                "wp_online_remaining_days_last_cycle": "-0.379",
                "ad_cust_res_cost_rate_last_cycle": "0.068",
                "reservation_cust_res_cnt_last_cycle": "0",
                "settle_cash_cost_rate_last_cycle": "-0.146",
                "sign_duration": "连续5年",
                "ad_cost": "63900.00",
                "comment_cnt_std_last_cycle": "0.062",
                "gd_navi_pv": "73374",
                "ai_msg_uv": "748",
                "ocpc_cost_last_cycle": "4.857",
                "ad_arrive_poi_cnt_last_cycle": "3.049",
                "settle_cash_cost_per_day_last_cycle": "4.873",
                "ad_cust_res_cnt_rate": "0.0097",
                "platform_leads_cnt_last_cycle": "10.000",
                "shop_id": "2021052000077000000020412572|xiniu_xgc_bgc",
                "invisible_kz_cnt_last_cycle": "0",
                "ad_exp_pv_per_day": "481562.50",
                "ai_msg_cnt_last_cycle": "3.561",
                "consult_kz_cnt": "47",
                "ad_cust_res_cost_last_cycle": "4.921",
                "invisible_kz_cnt_rate_last_cycle": "0",
                "arrive_kz_cnt_rate": "0.0000",
                "ord_cust_res_cnt_rate_last_cycle": "0",
                "ai_leads_card_exp_uv_rate_last_cycle": "-0.707",
                "redp_cost_last_cycle": "5.054",
                "cpc_cost_rate_last_cycle": "-0.265",
                "ai_voice_msg_cnt_last_cycle": "4.461",
                "gd_navi_pv_last_cycle": "2.760",
                "gmv": "9772.00",
                "security_guards_cnt": "3203",
                "ai_msg_uv_last_cycle": "3.426",
                "reservation_cust_res_cnt_rate": "0.0000",
                "ord_cnt_last_cycle": "0",
                "optimal_comment_cnt_std_last_cycle": "0.000",
                "business_situation": "良好",
                "cpc_cost_rate": "0.2205",
                "ord_cust_res_cnt_last_cycle": "0",
                "comment_cnt_last_cycle": "1.100",
                "ai_entry_exp_uv_last_cycle": "3.447",
                "use_suborder_cn_last_cycle": "0",
                "ai_msg_cnt_rate_last_cycle": "0.031",
                "invisible_kz_cnt_rate": "0.0162",
                "platform_leads_cnt_rate_last_cycle": "0.572",
                "reservation_cust_res_cnt": "0",
                "total_business_score": "10.0",
                "gd_car_kz_cnt_last_cycle": "0",
                "ad_kz_cnt_per_day_last_cycle": "5.861",
                "pic_cnt_plus": "46",
                "business_score": "7.2",
                "comment_cnt_std": "189",
                "ad_exp_pv": "963125",
                "gd_car_kz_cnt": "0",
                "miss_cust_res_cnt_last_cycle": "0",
                "ai_leads_card_exp_uv": "99",
                "settle_cash_cost_last_cycle": "4.874",
                "fst_sign_dt": "20210520",
                "total_route_pv": "25265",
                "clk_call_pv_last_cycle": "4.455",
                "call_cust_res_cnt": "94",
                "ord_cust_res_cnt_rate": "0.0324",
                "security_guards_cnt_last_cycle": "3.802",
                "ai_entry_clk_uv_last_cycle": "3.566",
                "ai_serve_card_exp_uv": "33",
                "platform_leads_cnt_rate": "0.2672",
                "arrive_kz_cnt_last_cycle": "0",
                "ngtv_comment_cnt_std": "3",
                "ai_fst_arrive_20m_uv_last_cycle": "0",
                "is_aiagent_online": "1",
                "gd_car_kz_cnt_rate_last_cycle": "0",
                "ocpc_cost": "36715.00",
                "reservation_cust_res_cnt_rate_last_cycle": "0",
                "right_label": "HIGH_V",
                "ord_cust_res_cnt": "8",
                "ad_kz_cnt_per_day": "123.50",
                "call_cust_res_cnt_rate": "0.3806",
                "ngtv_comment_cnt_std_last_cycle": "0.000",
                "settle_cash_cost_per_day": "23350.65",
                "total_route_pv_last_cycle": "3.290",
                "ad_cust_res_cost_per_thousand_last_cycle": "0.259",
                "wp_online_days": "1003",
                "use_gmv": "7756.00",
                "ai_leads_card_exp_uv_rate": "0.5470",
                "orig_call_end_cnt_last_cycle": "15.500",
                "shop_name": "四川省人民医院医疗集团新丽美医疗美容医院",
                "settle_cash_cost_rate": "148.64",
                "ad_clk_pv_rate_last_cycle": "-0.123",
                "ocpc_cost_rate": "0.5746",
                "ai_exp_uv_last_cycle": "1.237",
                "ad_clk_pv": "25345",
                "ad_cust_res_cnt_rate_last_cycle": "-0.030",
                "ad_arrive_poi_cnt": "2717",
                "ai_serve_card_exp_uv_last_cycle": "10.000",
                "wp_online_days_total_last_cycle": "0.090",
                "gmv_last_cycle": "0",
                "ad_cost_last_cycle": "4.921",
                "ai_leads_card_exp_uv_last_cycle": "0.768",
                "ai_msg_cnt_rate": "2.4332",
                "platform_leads_cnt": "66",
                "ai_arrive_20m_uv": "110",
                "ad_kz_cnt_last_cycle": "5.861",
                "redp_cost": "17198.00",
                "ai_entry_clk_uv": "799",
                "ocpc_cost_rate_last_cycle": "-0.011",
                "gd_car_kz_cnt_rate": "0.0000",
                "wp_online_days_total": "1003",
                "call_cust_res_cnt_last_cycle": "4.875",
                "district_name": "武侯区",
                "city_name": "成都",
                "wp_online_remaining_days": "136",
                "anti_hijacking_cnt": "0",
                "optimal_comment_cnt_std": "28",
                "wp_offline_days_last_cycle": "0",
                "ad_clk_pv_last_cycle": "3.526",
                "arrive_kz_cnt_rate_last_cycle": "0",
                "wp_online_days_last_cycle": "0.090",
                "shop_dish_num_last_cycle": "0",
                "consult_kz_cnt_rate": "0.1903",
                "use_gmv_last_cycle": "0",
                "miss_cust_res_cnt": "0",
                "canti_hijacking_cnt_last_cycle": "0",
                "ad_cust_res_cost_rate": "5.19",
                "ad_exp_pv_last_cycle": "3.703",
                "is_wp_online": "1",
                "gd_exp_pv_last_cycle": "3.622",
                "comment_cnt": "21",
                "consult_kz_cnt_rate_last_cycle": "0.002",
                "cpc_cost": "14089.00",
                "cpc_cost_last_cycle": "3.322",
                "call_cust_res_cnt_rate_last_cycle": "-0.135",
                "ai_entry_exp_uv": "11393",
                "ad_kz_cnt": "247"
              }
            ],
            "class": "com.amap.sales.data.client.dto.domainmodel.dto.vo.RuleDataVO",
            "retry": false,
            "extInfo": {},
            "specialValues": [
              {
                "keyValue": "OTHER",
                "class": "com.amap.sales.data.client.dto.domainmodel.dto.CommonPairDTO",
                "keyDesc": "喜报版本",
                "key": "businessNewType"
              },
              {
                "keyValue": "1",
                "class": "com.amap.sales.data.client.dto.domainmodel.dto.CommonPairDTO",
                "keyDesc": "智能体在约",
                "key": "is_aiagent_online"
              }
            ]
          }
    ],
    "success": true,
    "class": "com.amap.sales.data.client.dto.domainmodel.result.DataQueryResponse",
    "retry": false,
    "extInfo": {}
  },
  "retry": false,
  "errorMsg": "成功",
  "extInfo": {
    "traceId": "212b4c3b17556812330393415e742e"
  }
}
```

# 查询门店明细表格数据

---

年度喜报复用

#### 接口定义

```json
amap-sales-data.DataQueryGwFacade.queryData
```

#### 请求参数

| **参数名** | **类型** | **必填** | **说明** | **示例值** |
| --- | --- | --- | --- | --- |
| request | **DataQueryRequest** | 是 | 查询请求 | 见示例 |

**DataQueryRequest结构**

| **参数名** | **类型** | **是否必填** | **说明** | **示例值** |
| --- | --- | --- | --- | --- |
| applicationCode | String | 是 | 应用编码 | **"xibao\_shop\_detail\_0819****"** |
| requestChannel | String | 是 | 来源 | XUANYUAN("轩辕") |
| resultType | String | 是 | 返回类型 | 固定值：PAGE |
| pageSize | Integer | 是 | 每页数据量 | 10 |
| pageNo | Integer | 是 | 页码 | 1（从1开始） |
| requestParams | Object | 是 | 请求参数 | \- |
| requestParams.shopIdList | String | 与pid二选一 | 门店ID列表 | "xgc\_20241218115556755grku"，多个英文逗号“ ,” 隔开 |
| requestParams.pid | String | 与shopIdList二选一 | pid | "2088197979898989"，传了门店ID，则不生效pid筛选 |
| requestParams.startDate | String | 是 | 开始日期(yyyyMMdd) | "20250601" |
| requestParams.endDate | String | 是 | 结束日期(yyyyMMdd) | "20250607" |

#### 响应结构

| **参数名** | **类型** | **说明** | **示例值** |
| --- | --- | --- | --- |
| success | Boolean | 是否执行成功 | true |
| code | String | 响应码 | "0000" |
| msgInfo | String | 响应信息 | "调用成功" |
| data | BizResult | 业务响应数据 | 见示例 |

**BizResult**

| **参数名** | **类型** | **说明** | **示例值** |
| --- | --- | --- | --- |
| success | Boolean | 是否执行成功 | true |
| resultCode | String | 错误码 | "SUCCESS" |
| resultMessage | String | 错误原因 | "成功" |
| data | QueryResponse | 查询结果 | 见示例 |
| extInfo | Map<String, String> | 补充信息 | 见示例 |

**QueryResponse结构**

| **参数名** | **类型** | **说明** | **示例值** |
| --- | --- | --- | --- |
| applicationDataList | List<ApplicationDataVO> | 每个应用applicationCode的数据 | 见示例 |

**ApplicationDataVO结构**

| **参数名** | **类型** | **说明** | **示例值** |
| --- | --- | --- | --- |
| applicationCode | String | 应用code | 固定值 |
| ruleDataList | List<RuleDataVO> | 返回数据 | 见示例 |

#### 响应示例

```json
{
  "success": true,
  "errorCode": "SUCCESS",
  "class": "com.amap.sales.data.client.dto.domainmodel.result.Result",
  "value": {
    "applicationDataList": [
      {
        "ruleDataList": [
          {
            "ruleCode": "xibao_shop_detail_data_rule",
            "indicatorList": [],
            "success": false,
            "values": [
              {
                "ad_clk_pv_rate": "0.0263",
                "pic_cnt_plus_last_cycle": "-0.061",
                "ord_cnt": "12",
                "ai_arrive_20m_uv_rate_last_cycle": "0.131",
                "arrive_kz_cnt": "0",
                "ai_msg_cnt": "1820",
                "invisible_kz_cnt": "4",
                "item_cnt": "52",
                "ai_arrive_20m_uv_rate": "0.0405",
                "orig_call_end_cnt": "33",
                "ad_cust_res_cost": "63900.00",
                "ai_exp_uv": "132",
                "ai_voice_msg_cnt": "3233",
                "ai_arrive_20m_uv_last_cycle": "3.583",
                "use_suborder_cn": "9",
                "item_cnt_last_cycle": "0.106",
                "ad_exp_pv_per_day_last_cycle": "3.703",
                "gd_exp_pv": "1121907",
                "ad_cust_res_cost_per_thousand": "66.35",
                "consult_kz_cnt_last_cycle": "5.714",
                "settle_cash_cost": "46701.00",
                "wp_offline_days": "0",
                "clk_call_pv": "180",
                "wp_online_remaining_days_last_cycle": "-0.379",
                "ad_cust_res_cost_rate_last_cycle": "0.068",
                "reservation_cust_res_cnt_last_cycle": "0",
                "settle_cash_cost_rate_last_cycle": "-0.146",
                "ad_cost": "63900.00",
                "comment_cnt_std_last_cycle": "0.062",
                "gd_navi_pv": "73374",
                "ai_msg_uv": "748",
                "ocpc_cost_last_cycle": "4.857",
                "ad_arrive_poi_cnt_last_cycle": "3.049",
                "city_ad_kz_cost": "低",
                "settle_cash_cost_per_day_last_cycle": "4.873",
                "ad_cust_res_cnt_rate": "0.0097",
                "platform_leads_cnt_last_cycle": "10.000",
                "shop_id": "2021052000077000000020412572|xiniu_xgc_bgc",
                "invisible_kz_cnt_last_cycle": "0",
                "ad_exp_pv_per_day": "481562.50",
                "all_order_cnt_last_cycle": "0",
                "shop_vst_pv_last_cycle": "0",
                "ai_msg_cnt_last_cycle": "3.561",
                "consult_kz_cnt": "47",
                "ad_cust_res_cost_last_cycle": "4.921",
                "invisible_kz_cnt_rate_last_cycle": "0",
                "arrive_kz_cnt_rate": "0.0000",
                "ord_cust_res_cnt_rate_last_cycle": "0",
                "ai_leads_card_exp_uv_rate_last_cycle": "-0.707",
                "redp_cost_last_cycle": "5.054",
                "cpc_cost_rate_last_cycle": "-0.270",
                "ai_voice_msg_cnt_last_cycle": "4.461",
                "gd_navi_pv_last_cycle": "2.760",
                "gmv": "9772.00",
                "security_guards_cnt": "3203",
                "ai_msg_uv_last_cycle": "3.426",
                "reservation_cust_res_cnt_rate": "0.0000",
                "ord_cnt_last_cycle": "0",
                "optimal_comment_cnt_std_last_cycle": "0.000",
                "cpc_cost_rate": "0.2205",
                "ord_cust_res_cnt_last_cycle": "0",
                "comment_cnt_last_cycle": "1.100",
                "ai_entry_exp_uv_last_cycle": "3.447",
                "use_suborder_cn_last_cycle": "0",
                "ai_msg_cnt_rate_last_cycle": "0.031",
                "invisible_kz_cnt_rate": "0.0162",
                "platform_leads_cnt_rate_last_cycle": "0.603",
                "reservation_cust_res_cnt": "0",
                "total_business_score": "10.0",
                "ad_kz_cnt_per_district_last_cycle": "0.525",
                "ad_kz_cnt_per_district": "0.1411",
                "gd_car_kz_cnt_last_cycle": "0",
                "ad_kz_cnt_per_day_last_cycle": "5.861",
                "pic_cnt_plus": "46",
                "business_score": "7.2",
                "comment_cnt_std": "189",
                "ad_exp_pv": "963125",
                "gd_car_kz_cnt": "0",
                "miss_cust_res_cnt_last_cycle": "0",
                "online_order_cnt_last_cycle": "0",
                "ai_leads_card_exp_uv": "99",
                "settle_cash_cost_last_cycle": "4.874",
                "fst_sign_dt": "20210520",
                "total_route_pv": "25265",
                "clk_call_pv_last_cycle": "4.455",
                "call_cust_res_cnt": "94",
                "ord_cust_res_cnt_rate": "0.0324",
                "security_guards_cnt_last_cycle": "3.802",
                "ai_entry_clk_uv_last_cycle": "3.566",
                "ai_serve_card_exp_uv": "33",
                "platform_leads_cnt_rate": "0.2672",
                "arrive_kz_cnt_last_cycle": "0",
                "ds": "20250409~20250722",
                "ngtv_comment_cnt_std": "3",
                "ai_fst_arrive_20m_uv_last_cycle": "0",
                "is_aiagent_online": "1",
                "gd_car_kz_cnt_rate_last_cycle": "0",
                "ocpc_cost": "36715.00",
                "reservation_cust_res_cnt_rate_last_cycle": "0",
                "right_label": "HIGH_V",
                "ord_cust_res_cnt": "8",
                "ad_kz_cnt_per_day": "123.50",
                "call_cust_res_cnt_rate": "0.3806",
                "ngtv_comment_cnt_std_last_cycle": "0.000",
                "settle_cash_cost_per_day": "23350.65",
                "district_ad_kz_cost": "低",
                "total_route_pv_last_cycle": "3.290",
                "ad_cust_res_cost_per_thousand_last_cycle": "0.259",
                "ad_kz_cnt_per_city_last_cycle": "0.639",
                "wp_online_days": "1003",
                "use_gmv": "7756.00",
                "ai_leads_card_exp_uv_rate": "0.5470",
                "orig_call_end_cnt_last_cycle": "15.500",
                "shop_name": "四川省人民医院医疗集团新丽美医疗美容医院",
                "ad_kz_cnt_per_city": "0.0682",
                "settle_cash_cost_rate": "148.64",
                "ad_clk_pv_rate_last_cycle": "-0.037",
                "ocpc_cost_rate": "0.5746",
                "ai_exp_uv_last_cycle": "1.237",
                "ad_clk_pv": "25345",
                "ad_cust_res_cnt_rate_last_cycle": "0.516",
                "ad_arrive_poi_cnt": "2717",
                "ai_serve_card_exp_uv_last_cycle": "10.000",
                "wp_online_days_total_last_cycle": "0.090",
                "gmv_last_cycle": "0",
                "ad_cost_last_cycle": "4.921",
                "ai_leads_card_exp_uv_last_cycle": "0.768",
                "ai_msg_cnt_rate": "2.4332",
                "platform_leads_cnt": "66",
                "ai_arrive_20m_uv": "110",
                "ad_kz_cnt_last_cycle": "5.861",
                "redp_cost": "17198.00",
                "ai_entry_clk_uv": "799",
                "ocpc_cost_rate_last_cycle": "-0.011",
                "gd_car_kz_cnt_rate": "0.0000",
                "wp_online_days_total": "1003",
                "call_cust_res_cnt_last_cycle": "4.875",
                "district_name": "武侯区",
                "city_name": "成都",
                "wp_online_remaining_days": "136",
                "anti_hijacking_cnt": "0",
                "optimal_comment_cnt_std": "28",
                "wp_offline_days_last_cycle": "0",
                "ad_clk_pv_last_cycle": "3.526",
                "arrive_kz_cnt_rate_last_cycle": "0",
                "wp_online_days_last_cycle": "0.090",
                "shop_dish_num_last_cycle": "0",
                "consult_kz_cnt_rate": "0.1903",
                "ticket_order_cnt_last_cycle": "0",
                "use_gmv_last_cycle": "0",
                "canti_hijacking_cnt_last_cycle": "0",
                "ad_cust_res_cost_rate": "5.19",
                "phone_order_cnt_last_cycle": "0",
                "ad_exp_pv_last_cycle": "3.703",
                "gd_exp_pv_last_cycle": "3.622",
                "is_wp_online": "1",
                "comment_cnt": "21",
                "consult_kz_cnt_rate_last_cycle": "-0.021",
                "cpc_cost": "14089.00",
                "cpc_cost_last_cycle": "3.322",
                "call_cust_res_cnt_rate_last_cycle": "-0.144",
                "ai_entry_exp_uv": "11393",
                "ad_kz_cnt": "247"
              }
            ],
            "class": "com.amap.sales.data.client.dto.domainmodel.dto.vo.RuleDataVO",
            "retry": false,
            "extInfo": {},
            "specialValues": [
              {
                "keyValue": "OTHER",
                "class": "com.amap.sales.data.client.dto.domainmodel.dto.CommonPairDTO",
                "keyDesc": "喜报版本",
                "key": "businessNewType"
              },
              {
                "keyValue": "1",
                "class": "com.amap.sales.data.client.dto.domainmodel.dto.CommonPairDTO",
                "keyDesc": "智能体在约",
                "key": "is_aiagent_online"
              }
            ]
          }
        ],
        "class": "com.amap.sales.data.client.dto.domainmodel.dto.vo.ApplicationDataVO",
        "applicationCode": "xibao_shop_fix_multi_ids"
      }
    ],
    "success": true,
    "class": "com.amap.sales.data.client.dto.domainmodel.result.DataQueryResponse",
    "retry": false,
    "extInfo": {}
  },
  "retry": false,
  "errorMsg": "成功",
  "extInfo": {
    "traceId": "212b4c3b17556812330393415e742e"
  }
}
```

# 查询同行数据

#### 接口定义

```json
amap-sales-data.DataQueryGwFacade.queryData
```

#### 请求参数

| **参数名** | **类型** | **必填** | **说明** | **示例值** |
| --- | --- | --- | --- | --- |
| request | **DataQueryRequest** | 是 | 查询请求 | 见示例 |

**DataQueryRequest结构**

| **参数名** | **类型** | **是否必填** | **说明** | **示例值** |
| --- | --- | --- | --- | --- |
| applicationCode | String | 是 | 应用编码 | **"xibao\_shop\_peers\_0819****"** |
| requestChannel | String | 是 | 来源 | XUANYUAN("轩辕") |
| resultType | String | 是 | 返回类型 | 固定值：LSIT |
| requestParams | Object | 是 | 请求参数 | \- |
| requestParams.shopId | String | 是 | 门店ID | "xgc\_20241218115556755grku" |
| requestParams.startDate | String | 是 | 开始日期(yyyyMMdd) | "20250601" |
| requestParams.endDate | String | 是 | 结束日期(yyyyMMdd) | "20250607" |
| requestParams.range | String | 是 | 范围 | "NORMAL/CITY/DISTRICT"（普通五公里，同城，同区） |
| requestParams.peerKeyList | String | 是 | 需要查询同行的字段,多个英文","隔开 | "exposure\_pv,exposure\_uv" |

#### 响应结构

| **参数名** | **类型** | **说明** | **示例值** |
| --- | --- | --- | --- |
| success | Boolean | 是否执行成功 | true |
| code | String | 响应码 | "0000" |
| msgInfo | String | 响应信息 | "调用成功" |
| data | BizResult | 业务响应数据 | 见示例 |

**BizResult**

| **参数名** | **类型** | **说明** | **示例值** |
| --- | --- | --- | --- |
| success | Boolean | 是否执行成功 | true |
| resultCode | String | 错误码 | "SUCCESS" |
| resultMessage | String | 错误原因 | "成功" |
| data | QueryResponse | 查询结果 | 见示例 |
| extInfo | Map<String, String> | 补充信息 | 见示例 |

**QueryResponse结构**

| **参数名** | **类型** | **说明** | **示例值** |
| --- | --- | --- | --- |
| applicationDataList | List<ApplicationDataVO> | 每个应用applicationCode的数据 | 见示例 |

**ApplicationDataVO结构**

| **参数名** | **类型** | **说明** | **示例值** |
| --- | --- | --- | --- |
| applicationCode | String | 应用code | 固定值 |
| ruleDataList | List<RuleDataVO> | 返回数据 | 见示例 |

#### 响应示例

```json
{
  "success": true,
  "errorCode": "SUCCESS",
  "class": "com.amap.sales.data.client.dto.domainmodel.result.Result",
  "value": {
    "applicationDataList": [
      {
        "ruleDataList": [
          {
            "ruleCode": "xibao_shop_detail_data_rule",
            "indicatorList": [],
            "success": false,
            "values": [
              {
                "exposure_pv": "100",
                "exposure_pv_vs_peers_diff": "-50",
                "exposure_pv_vs_peers": "50",
                "exposure_uv": "100",
                "exposure_uv_vs_peers_diff": "-50",
                "exposure_uv_vs_peers": "50"
              }
            ],
            "class": "com.amap.sales.data.client.dto.domainmodel.dto.vo.RuleDataVO",
            "retry": false,
            "extInfo": {},
            "specialValues": [
            ]
          }
        ],
        "class": "com.amap.sales.data.client.dto.domainmodel.dto.vo.ApplicationDataVO",
        "applicationCode": "xibao_shop_fix_multi_ids"
      }
    ],
    "success": true,
    "class": "com.amap.sales.data.client.dto.domainmodel.result.DataQueryResponse",
    "retry": false,
    "extInfo": {}
  },
  "retry": false,
  "errorMsg": "成功",
  "extInfo": {
    "traceId": "212b4c3b17556812330393415e742e"
  }
}
```

# 对比门店筛选

#### 接口定义

```json
amap-sales-data.DataQueryGwFacade.queryData
```

#### 请求参数

| **参数名** | **类型** | **必填** | **说明** | **示例值** |
| --- | --- | --- | --- | --- |
| request | **DataQueryRequest** | 是 | 查询请求 | 见示例 |

**DataQueryRequest结构**

| **参数名** | **类型** | **是否必填** | **说明** | **示例值** |
| --- | --- | --- | --- | --- |
| applicationCode | String | 是 | 应用编码 | **"xibao\_shop\_same\_atag\_select\_0819****"** |
| requestChannel | String | 是 | 来源 | XUANYUAN("轩辕") |
| resultType | String | 是 | 返回类型 | 固定值：LIST |
| requestParams | Object | 是 | 请求参数 | \- |
| requestParams.shopId | String | 门店ID | 门店ID | "xgc\_20241218115556755grku" |
| requestParams.startDate | String | 是 | 开始日期(yyyyMMdd) | "20250601" |
| requestParams.endDate | String | 是 | 结束日期(yyyyMMdd) | "20250607" |

#### 响应结构

| **参数名** | **类型** | **说明** | **示例值** |
| --- | --- | --- | --- |
| success | Boolean | 是否执行成功 | true |
| code | String | 响应码 | "0000" |
| msgInfo | String | 响应信息 | "调用成功" |
| data | BizResult | 业务响应数据 | 见示例 |

**BizResult**

| **参数名** | **类型** | **说明** | **示例值** |
| --- | --- | --- | --- |
| success | Boolean | 是否执行成功 | true |
| resultCode | String | 错误码 | "SUCCESS" |
| resultMessage | String | 错误原因 | "成功" |
| data | QueryResponse | 查询结果 | 见示例 |
| extInfo | Map<String, String> | 补充信息 | 见示例 |

**QueryResponse结构**

| **参数名** | **类型** | **说明** | **示例值** |
| --- | --- | --- | --- |
| applicationDataList | List<ApplicationDataVO> | 每个应用applicationCode的数据 | 见示例 |

**ApplicationDataVO结构**

| **参数名** | **类型** | **说明** | **示例值** |
| --- | --- | --- | --- |
| applicationCode | String | 应用code | 固定值 |
| ruleDataList | List<RuleDataVO> | 返回数据 | 见示例 |

#### 响应示例

```json
{
  "success": true,
  "errorCode": "SUCCESS",
  "class": "com.amap.sales.data.client.dto.domainmodel.result.Result",
  "value": {
    "applicationDataList": [
      {
        "ruleDataList": [
          {
            "ruleCode": "xibao_shop_detail_data_rule",
            "indicatorList": [],
            "success": false,
            "values": [
              {
                "shop_id":"xgc_202409191724023155bji|xiniu_xgc_bgc",
                "match_shopid":"xgc_20250513104003181cbar|xiniu_xgc_bgc",
                "match_shopname":"雅芳婷床垫(华美居旗舰店)",
                "right_label":"HIGH_V"
              }
            ],
            "class": "com.amap.sales.data.client.dto.domainmodel.dto.vo.RuleDataVO",
            "retry": false,
            "extInfo": {},
            "specialValues": [
            ]
          }
        ],
        "class": "com.amap.sales.data.client.dto.domainmodel.dto.vo.ApplicationDataVO",
        "applicationCode": "xibao_shop_fix_multi_ids"
      }
    ],
    "success": true,
    "class": "com.amap.sales.data.client.dto.domainmodel.result.DataQueryResponse",
    "retry": false,
    "extInfo": {}
  },
  "retry": false,
  "errorMsg": "成功",
  "extInfo": {
    "traceId": "212b4c3b17556812330393415e742e"
  }
}
```

# 同业门店对比数据

#### 接口定义

```json
amap-sales-data.DataQueryGwFacade.queryData
```

#### 请求参数

| **参数名** | **类型** | **必填** | **说明** | **示例值** |
| --- | --- | --- | --- | --- |
| request | **DataQueryRequest** | 是 | 查询请求 | 见示例 |

**DataQueryRequest结构**

| **参数名** | **类型** | **是否必填** | **说明** | **示例值** |
| --- | --- | --- | --- | --- |
| applicationCode | String | 是 | 应用编码 | **"xibao\_shop\_same\_atag\_data\_compare\_0819****"** |
| requestChannel | String | 是 | 来源 | XUANYUAN("轩辕") |
| resultType | String | 是 | 返回类型 | 固定值：LIST |
| requestParams | Object | 是 | 请求参数 | \- |
| requestParams.shopIdList | String | 是 | 门店ID列表 | "xgc\_20241218115556755grku" |
| requestParams.businessNewType | String | 是 | 版本 | "FOOD" |
| requestParams.startDate | String | 是 | 开始日期(yyyyMMdd) | "20250601" |
| requestParams.endDate | String | 是 | 结束日期(yyyyMMdd) | "20250607" |

#### 响应结构

| **参数名** | **类型** | **说明** | **示例值** |
| --- | --- | --- | --- |
| success | Boolean | 是否执行成功 | true |
| code | String | 响应码 | "0000" |
| msgInfo | String | 响应信息 | "调用成功" |
| data | BizResult | 业务响应数据 | 见示例 |

**BizResult**

| **参数名** | **类型** | **说明** | **示例值** |
| --- | --- | --- | --- |
| success | Boolean | 是否执行成功 | true |
| resultCode | String | 错误码 | "SUCCESS" |
| resultMessage | String | 错误原因 | "成功" |
| data | QueryResponse | 查询结果 | 见示例 |
| extInfo | Map<String, String> | 补充信息 | 见示例 |

**QueryResponse结构**

| **参数名** | **类型** | **说明** | **示例值** |
| --- | --- | --- | --- |
| applicationDataList | List<ApplicationDataVO> | 每个应用applicationCode的数据 | 见示例 |

**ApplicationDataVO结构**

| **参数名** | **类型** | **说明** | **示例值** |
| --- | --- | --- | --- |
| applicationCode | String | 应用code | 固定值 |
| ruleDataList | List<RuleDataVO> | 返回数据 | 见示例 |

#### 响应示例

```json
    ○ （美食版指标）
        ■ 曝光量  exposure_pv
        ■ 访问量  store_view_pv
        ■ 预约到店量 shop_order_cnt
        ■ 电话拨打量  phone_call_order_cnt
        ■ 广告有效到店量  arrive_poi_cnt
        ■ 广告有效到店成本  arrive_settle_cost_rate
    ○ （非美食版指标）
        ■ 门店曝光量  exposure_pv
        ■ 来电量  call_end_cnt
        ■ 广告客资量  bill_leads_cnt
        ■ 广告客资成本  leads_settle_cost_rate
        ■ 广告有效到店量 arrive_poi_cnt
        ■ 商家分  business_score
```

```json
sh{
  "success": true,
  "errorCode": "SUCCESS",
  "class": "com.amap.sales.data.client.dto.domainmodel.result.Result",
  "value": {
    "applicationDataList": [
      {
        "mergeData": null,
        "ruleDataList": [
          {
            "values": [
              {
                "exposure_pv": "23506",
                "call_end_cnt": "412",
                "bill_leads_cnt": "0",
                "leads_settle_cost_rate": "422",
                "arrive_poi_cnt": "0",
                "business_score": "36",
                "shop_id": "xgc_20241111100019638ppht|xiniu_xgc_bgc"
              },
              {
                "exposure_pv": "23506",
                "call_end_cnt": "412",
                "bill_leads_cnt": "0",
                "leads_settle_cost_rate": "422",
                "arrive_poi_cnt": "0",
                "business_score": "36",
                "shop_id": "xgc_20241111100019638ppht|xiniu_xgc_bgc"
              }
            ],
            "pageSize": 0,
            "errorCode": null,
            "displayFields": [
            ],
            "errorMsg": null,
            "extInfo": {
              "variableConf": "[{\"canModify\":true,\"defaultValue\":\"OTHER\",\"required\":false,\"variableCode\":\"businessNewType\",\"variableName\":\"版本\"},{\"canModify\":true,\"defaultValue\":\"20250505\",\"required\":false,\"variableCode\":\"startDate\",\"variableName\":\"开始\"},{\"canModify\":true,\"defaultValue\":\"20250505\",\"required\":false,\"variableCode\":\"endDate\",\"variableName\":\"结束\"},{\"canModify\":true,\"defaultValue\":\"2024081511077000000110000142|xiniu_xgc_bgc\",\"required\":false,\"variableCode\":\"shopIdList\",\"variableName\":\"门店\"}]"
            },
            "ruleCode": "xibao_year_report_same_atag_shop_compare_rule",
            "totalSize": 0,
            "indicatorList": [],
            "metaColumn": [
              "gd_exp_pv",
              "shop_vst_pv",
              "ticket_order_cnt",
              "gd_navi_pv",
              "phone_order_cnt",
              "arrive_poi_cnt",
              "settle_cost_rate",
              "shop_id"
            ],
            "pageNo": 0,
            "success": false,
            "ruleName": "年度喜报查询规则-预发-同业门店对比",
            "ruleId": "20250508000100482001",
            "class": "com.amap.sales.data.client.dto.domainmodel.dto.vo.RuleDataVO",
            "retry": false,
            "specialValues": [
              {
                "keyValue": "FOOD",
                "class": "com.amap.sales.data.client.dto.domainmodel.dto.CommonPairDTO",
                "keyDesc": "喜报版本",
                "key": "businessNewType"
              }
            ]
          }
        ],
        "class": "com.amap.sales.data.client.dto.domainmodel.dto.vo.ApplicationDataVO",
        "applicationCode": "xibao_year_report_same_atag_shop_compare_application"
      }
    ],
    "success": true,
    "errorCode": null,
    "class": "com.amap.sales.data.client.dto.domainmodel.result.DataQueryResponse",
    "retry": false,
    "errorMsg": null,
    "extInfo": {}
  },
  "retry": false,
  "errorMsg": "成功",
  "extInfo": {
    "traceId": "213f885617561773323402760e59bf"
  }
}
```

# 喜报数据下载

---

年度喜报下载复用

#### 下载场景码

**xibao\_shop\_download\_0819**

#### 请求参数

| **参数名** | **类型** | **必填** | **说明** | **示例值** |
| --- | --- | --- | --- | --- |
| shopIdList | String | 是 | 门店ID列表 | "xgc\_20241218115556755grku" |
| pid | String | 是 | 与门店二选一 | 2088898989898989 |
| businessNewType | String | 是 | 版本 | "FOOD" |
| startDate | String | 是 | 开始日期(yyyyMMdd) | "20250601" |
| endDate | String | 是 | 结束日期(yyyyMMdd) | "20250607" |